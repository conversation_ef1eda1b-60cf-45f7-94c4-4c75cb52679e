<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Haroon Shopping Center</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet" />
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js"></script>
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="https://cdn.jsdelivr.net/npm/animejs@3.2.1/lib/anime.min.js"></script>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");
      :root {
        --primary-color: #c62828;
        --primary-dark: #8e0000;
        --primary-light: #ff5f52;
      }
      body {
        font-family: "Poppins", sans-serif;
        scroll-behavior: smooth;
      }
      .text-glow {
        animation: glow 2s ease-in-out infinite alternate;
      }
      @keyframes glow {
        from {
          text-shadow: 0 0 10px #fff, 0 0 20px #fff,
            0 0 30px var(--primary-light), 0 0 40px var(--primary-light);
        }
        to {
          text-shadow: 0 0 20px #fff, 0 0 30px var(--primary-color),
            0 0 40px var(--primary-color), 0 0 50px var(--primary-color);
        }
      }
      .hero-overlay {
        background: linear-gradient(
          to right,
          rgba(0, 0, 0, 0.5),
          rgba(198, 40, 40, 0.3)
        );
      }
      .floating-btn {
        transition: all 0.3s ease;
      }
      .floating-btn:hover {
        transform: scale(1.1);
      }
      .floating-menu {
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        transform: translateY(20px);
      }
      .floating-menu.active {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }
      .floor-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }
    </style>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {
                DEFAULT: "#c62828",
                dark: "#8e0000",
                light: "#ff5f52",
              },
            },
          },
        },
      };
    </script>
  </head>
  <body class="bg-gray-50">
    <!-- Header with Banner -->
    <header class="relative w-full h-96 md:h-screen/2 overflow-hidden">
      <img
        src="/static/banner.jpeg"
        alt="Haroon Shopping Center"
        class="w-full h-full object-cover"
      />
      <div
        class="absolute inset-0 hero-overlay flex items-center justify-center"
      >
        <h1
          class="text-4xl md:text-6xl lg:text-8xl font-bold text-white text-glow"
          data-aos="zoom-in"
          data-aos-duration="1000"
        >
          HAROON SHOPPING CENTER
        </h1>
      </div>
    </header>

    <!-- Timing Section -->
    <section class="py-8 bg-primary text-white" data-aos="fade-up">
      <div class="container mx-auto px-4">
        <div class="text-center">
          <h2 class="text-2xl md:text-3xl font-bold mb-2">Opening Hours</h2>
          <p class="text-xl">Monday - Thursday: 9:00 AM to 9:00 PM</p>
          <p class="text-xl">Friday: 9:00 AM to 3:00 PM</p>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center" data-aos="fade-up">
          <h2 class="text-3xl md:text-4xl font-bold text-primary-dark mb-6">
            Welcome to Haroon Shopping Center
          </h2>
          <p class="text-lg text-gray-700 mb-8">
            Haroon Shopping Center is a modern retail destination offering a
            wide variety of shops and services under one roof. With ten floors
            of carefully curated shopping experiences, we bring together local
            and international brands for a complete shopping experience.
          </p>
        </div>
      </div>
    </section>

    <!-- Floors Section -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2
          class="text-3xl md:text-4xl font-bold text-primary-dark text-center mb-12"
          data-aos="fade-up"
        >
          Explore Our Floors
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Floor 1 -->
          <div
            class="bg-white rounded-lg shadow-lg overflow-hidden floor-card transition duration-300"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <img
              src="http://static.photos/fashion/640x360/1"
              alt="Men's Clothing"
              class="w-full h-48 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-bold text-primary-dark mb-2">
                1st Floor
              </h3>
              <p class="text-gray-600">
                Exclusive collection of men's clothing shops featuring the
                latest trends and styles.
              </p>
            </div>
          </div>

          <!-- Floor 2 -->
          <div
            class="bg-white rounded-lg shadow-lg overflow-hidden floor-card transition duration-300"
            data-aos="fade-up"
            data-aos-delay="150"
          >
            <img
              src="http://static.photos/technology/640x360/2"
              alt="Mobile Accessories"
              class="w-full h-48 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-bold text-primary-dark mb-2">
                2nd Floor
              </h3>
              <p class="text-gray-600">
                Mobile accessories, watches, clocks, and artificial jewelry from
                top brands.
              </p>
            </div>
          </div>

          <!-- Floor 3 -->
          <div
            class="bg-white rounded-lg shadow-lg overflow-hidden floor-card transition duration-300"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <img
              src="http://static.photos/children/640x360/3"
              alt="Children's Wear"
              class="w-full h-48 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-bold text-primary-dark mb-2">
                3rd Floor
              </h3>
              <p class="text-gray-600">
                Children's wear, boys' and girls' clothing, and accessories for
                all ages.
              </p>
            </div>
          </div>

          <!-- Floor 4 -->
          <div
            class="bg-white rounded-lg shadow-lg overflow-hidden floor-card transition duration-300"
            data-aos="fade-up"
            data-aos-delay="250"
          >
            <img
              src="http://static.photos/beauty/640x360/4"
              alt="Cosmetics"
              class="w-full h-48 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-bold text-primary-dark mb-2">
                4th Floor
              </h3>
              <p class="text-gray-600">
                Cosmetics and perfumes for both men and women, featuring
                international brands.
              </p>
            </div>
          </div>

          <!-- Floor 5 -->
          <div
            class="bg-white rounded-lg shadow-lg overflow-hidden floor-card transition duration-300"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            <img
              src="http://static.photos/footwear/640x360/5"
              alt="Shoes"
              class="w-full h-48 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-bold text-primary-dark mb-2">
                5th Floor
              </h3>
              <p class="text-gray-600">
                Male and female shoes with a wide selection for all ages and
                occasions.
              </p>
            </div>
          </div>

          <!-- Floor 6 -->
          <div
            class="bg-white rounded-lg shadow-lg overflow-hidden floor-card transition duration-300"
            data-aos="fade-up"
            data-aos-delay="350"
          >
            <img
              src="http://static.photos/home/<USER>/6"
              alt="Home Textiles"
              class="w-full h-48 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-bold text-primary-dark mb-2">
                6th Floor
              </h3>
              <p class="text-gray-600">
                Garments, home textiles, and stylish home decoration items for
                your living space.
              </p>
            </div>
          </div>

          <!-- Floor 7 -->
          <div
            class="bg-white rounded-lg shadow-lg overflow-hidden floor-card transition duration-300"
            data-aos="fade-up"
            data-aos-delay="400"
          >
            <img
              src="http://static.photos/fashion/640x360/7"
              alt="Women's Apparel"
              class="w-full h-48 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-bold text-primary-dark mb-2">
                7th Floor
              </h3>
              <p class="text-gray-600">
                Women's apparel and a dedicated cosmetic mall with premium
                beauty products.
              </p>
            </div>
          </div>

          <!-- Floor 8 -->
          <div
            class="bg-white rounded-lg shadow-lg overflow-hidden floor-card transition duration-300"
            data-aos="fade-up"
            data-aos-delay="450"
          >
            <img
              src="http://static.photos/wedding/640x360/8"
              alt="Wedding Attire"
              class="w-full h-48 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-bold text-primary-dark mb-2">
                8th Floor
              </h3>
              <p class="text-gray-600">
                Wedding attire and bridal accessories for your special day.
              </p>
            </div>
          </div>

          <!-- Floor 9 -->
          <div
            class="bg-white rounded-lg shadow-lg overflow-hidden floor-card transition duration-300"
            data-aos="fade-up"
            data-aos-delay="500"
          >
            <img
              src="http://static.photos/accessories/640x360/9"
              alt="Bags and Luggage"
              class="w-full h-48 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-bold text-primary-dark mb-2">
                9th Floor
              </h3>
              <p class="text-gray-600">
                Bags, handbags, luggage, and sofas. Spacious restrooms
                available.
              </p>
            </div>
          </div>

          <!-- Floor 10 -->
          <div
            class="bg-white rounded-lg shadow-lg overflow-hidden floor-card transition duration-300"
            data-aos="fade-up"
            data-aos-delay="550"
          >
            <img
              src="http://static.photos/office/640x360/10"
              alt="Offices"
              class="w-full h-48 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-bold text-primary-dark mb-2">
                10th Floor
              </h3>
              <p class="text-gray-600">
                Staff and management offices. Information and administration
                services.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Experience Section -->
    <section class="py-16 bg-primary text-white">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center" data-aos="fade-up">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">
            A Complete Shopping Experience
          </h2>
          <p class="text-lg mb-8">
            Each section features high-quality products and attentive customer
            service, making every visit convenient and enjoyable for families
            and individuals. Haroon Shopping Center brings together local and
            international brands under one roof, creating the ultimate shopping
            destination.
          </p>
        </div>
      </div>
    </section>

    <!-- Location Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row items-center">
          <div class="md:w-1/2 mb-8 md:mb-0 md:pr-8" data-aos="fade-right">
            <h2 class="text-3xl font-bold text-primary-dark mb-4">
              Our Location
            </h2>
            <p class="text-gray-700 mb-4">
              Visit us at our convenient location in the heart of the city.
              We're easily accessible with ample parking space available.
            </p>
            <address class="text-lg not-italic mb-4">
              <strong>Haroon Shopping Center</strong><br />
              123 Main Commercial Area<br />
              City Center, Quetta<br />
              Pakistan
            </address>
            <a
              href="https://maps.app.goo.gl/aMA3E5EB25ZsSYeg9"
              target="_blank"
              class="inline-flex items-center text-primary hover:text-primary-dark"
            >
              <i data-feather="map-pin" class="mr-2"></i>
              View on Google Maps
            </a>
          </div>
          <div class="md:w-1/2" data-aos="fade-left">
            <img
              src="http://static.photos/cityscape/640x360/11"
              alt="Shopping Center Location"
              class="w-full rounded-lg shadow-lg"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Team Section -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2
          class="text-3xl font-bold text-primary-dark text-center mb-12"
          data-aos="fade-up"
        >
          Our Leadership
        </h2>

        <div class="flex flex-col md:flex-row justify-center gap-8">
          <!-- Owner -->
          <div
            class="bg-white p-6 rounded-lg shadow-lg text-center max-w-xs mx-auto"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <img
              src="http://static.photos/people/320x240/12"
              alt="Dr. Haroon Khan"
              class="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
            />
            <h3 class="text-xl font-bold text-primary-dark mb-2">
              Dr. Haroon Khan
            </h3>
            <p class="text-gray-600 mb-2">Founder & Owner</p>
            <p class="text-gray-700">
              A visionary entrepreneur with decades of experience in retail, Dr.
              Haroon Khan established the shopping center to provide a premium
              shopping experience to the community.
            </p>
          </div>

          <!-- Manager -->
          <div
            class="bg-white p-6 rounded-lg shadow-lg text-center max-w-xs mx-auto"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <img
              src="http://static.photos/people/320x240/13"
              alt="Waheed Khan"
              class="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
            />
            <h3 class="text-xl font-bold text-primary-dark mb-2">
              Waheed Khan
            </h3>
            <p class="text-gray-600 mb-2">Managing Director</p>
            <p class="text-gray-700">
              With exceptional leadership skills, Waheed Khan oversees daily
              operations, ensuring the shopping center maintains its high
              standards of service and quality.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary-dark text-white py-12">
      <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row justify-between">
          <div class="mb-8 md:mb-0">
            <h3 class="text-2xl font-bold mb-4">Haroon Shopping Center</h3>
            <p class="mb-4">Your ultimate shopping destination since 2005.</p>
            <div class="flex space-x-4">
              <a
                href="https://www.facebook.com/share/1FL5sYMhL9/"
                target="_blank"
                class="hover:text-primary-light"
              >
                <i data-feather="facebook"></i>
              </a>
              <a
                href="https://www.instagram.com/haroonshoppingcenter8"
                target="_blank"
                class="hover:text-primary-light"
              >
                <i data-feather="instagram"></i>
              </a>
              <a
                href="https://youtube.com/@haroonshoppingcenterquetta2635"
                target="_blank"
                class="hover:text-primary-light"
              >
                <i data-feather="youtube"></i>
              </a>
            </div>
          </div>
          <div class="grid grid-cols-2 gap-8">
            <div>
              <h4 class="text-lg font-bold mb-4">Quick Links</h4>
              <ul class="space-y-2">
                <li><a href="#" class="hover:text-primary-light">Home</a></li>
                <li>
                  <a href="#floors" class="hover:text-primary-light">Floors</a>
                </li>
                <li>
                  <a href="#location" class="hover:text-primary-light"
                    >Location</a
                  >
                </li>
                <li>
                  <a href="#team" class="hover:text-primary-light">Our Team</a>
                </li>
              </ul>
            </div>
            <div>
              <h4 class="text-lg font-bold mb-4">Contact</h4>
              <ul class="space-y-2">
                <li class="flex items-center">
                  <i data-feather="phone" class="mr-2"></i>
                  <span>+92 XXX XXXXXXX</span>
                </li>
                <li class="flex items-center">
                  <i data-feather="mail" class="mr-2"></i>
                  <span><EMAIL></span>
                </li>
                <li class="flex items-center">
                  <i data-feather="map-pin" class="mr-2"></i>
                  <span>Quetta, Pakistan</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="border-t border-primary mt-8 pt-8 text-center">
          <p>&copy; 2023 Haroon Shopping Center. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- Floating Contact Button -->
    <div class="fixed bottom-8 right-8 z-50">
      <button
        id="floatingBtn"
        class="floating-btn bg-primary text-white rounded-full p-4 shadow-lg hover:shadow-xl"
      >
        <i data-feather="message-square"></i>
      </button>
      <div
        id="floatingMenu"
        class="floating-menu absolute bottom-16 right-0 bg-white rounded-lg shadow-lg p-4 w-64"
      >
        <div class="space-y-3">
          <a
            href="tel:+923001234567"
            class="flex items-center text-gray-800 hover:text-primary"
          >
            <i data-feather="phone" class="mr-2"></i>
            <span>Call Us</span>
          </a>
          <a
            href="https://wa.me/923001234567"
            target="_blank"
            class="flex items-center text-gray-800 hover:text-primary"
          >
            <i data-feather="message-circle" class="mr-2"></i>
            <span>WhatsApp</span>
          </a>
          <a
            href="mailto:<EMAIL>"
            class="flex items-center text-gray-800 hover:text-primary"
          >
            <i data-feather="mail" class="mr-2"></i>
            <span>Email Us</span>
          </a>
          <a
            href="https://www.facebook.com/share/1FL5sYMhL9/"
            target="_blank"
            class="flex items-center text-gray-800 hover:text-primary"
          >
            <i data-feather="facebook" class="mr-2"></i>
            <span>Facebook</span>
          </a>
          <a
            href="https://maps.app.goo.gl/aMA3E5EB25ZsSYeg9"
            target="_blank"
            class="flex items-center text-gray-800 hover:text-primary"
          >
            <i data-feather="map" class="mr-2"></i>
            <span>View Map</span>
          </a>
        </div>
      </div>
    </div>

    <script>
      // Initialize AOS
      AOS.init({
        duration: 800,
        easing: "ease-in-out",
        once: true,
      });

      // Initialize Feather Icons
      feather.replace();

      // Floating Button Interaction
      const floatingBtn = document.getElementById("floatingBtn");
      const floatingMenu = document.getElementById("floatingMenu");

      floatingBtn.addEventListener("click", () => {
        floatingMenu.classList.toggle("active");
      });

      // Close menu when clicking outside
      document.addEventListener("click", (e) => {
        if (
          !floatingBtn.contains(e.target) &&
          !floatingMenu.contains(e.target)
        ) {
          floatingMenu.classList.remove("active");
        }
      });

      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          document.querySelector(this.getAttribute("href")).scrollIntoView({
            behavior: "smooth",
          });
        });
      });

      // Text glow animation
      anime({
        targets: ".text-glow",
        opacity: [0.8, 1],
        duration: 2000,
        loop: true,
        direction: "alternate",
        easing: "easeInOutSine",
      });
    </script>
  </body>
</html>
