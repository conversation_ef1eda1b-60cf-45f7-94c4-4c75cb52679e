<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Haroon Shopping Center</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet" />
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js"></script>
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="https://cdn.jsdelivr.net/npm/animejs@3.2.1/lib/anime.min.js"></script>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");
      :root {
        --primary-color: #c62828;
        --primary-dark: #8e0000;
        --primary-light: #ff5f52;
      }
      * {
        box-sizing: border-box;
      }
      body {
        font-family: "Poppins", sans-serif;
        scroll-behavior: smooth;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
      }
      .text-glow {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      }
      .hero-overlay {
        background: linear-gradient(
          to right,
          rgba(0, 0, 0, 0.5),
          rgba(198, 40, 40, 0.3)
        );
      }
      .floating-btn {
        transition: all 0.3s ease;
      }
      .floating-btn:hover {
        transform: scale(1.1);
      }
      .floating-menu {
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        transform: translateY(20px);
      }
      .floating-menu.active {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
        box-shadow: 0 20px 25px -5px rgba(198, 40, 40, 0.3),
          0 10px 10px -5px rgba(198, 40, 40, 0.2);
      }

      /* Navigation Styles */
      .navbar {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        transition: all 0.3s ease;
      }
      .navbar.scrolled {
        background: rgba(255, 255, 255, 0.98);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
      }

      /* Wave Styles */
      .wave-top {
        position: relative;
      }
      .wave-top::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 60px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' fill='%23ffffff'%3E%3C/path%3E%3C/svg%3E")
          no-repeat;
        background-size: cover;
        z-index: 1;
      }
      .wave-bottom {
        position: relative;
      }
      .wave-bottom::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 60px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z' fill='%23ffffff'%3E%3C/path%3E%3C/svg%3E")
          no-repeat;
        background-size: cover;
        z-index: 1;
      }

      /* Gradient Wave Styles */
      .wave-gradient-top {
        position: relative;
      }
      .wave-gradient-top::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 60px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' fill='url(%23gradient1)'%3E%3C/path%3E%3Cdefs%3E%3ClinearGradient id='gradient1' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23c62828;stop-opacity:0.1'/%3E%3Cstop offset='100%25' style='stop-color:%23ff5f52;stop-opacity:0.1'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E")
          no-repeat;
        background-size: cover;
        z-index: 1;
      }
      .wave-gradient-bottom {
        position: relative;
      }
      .wave-gradient-bottom::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 60px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z' fill='url(%23gradient2)'%3E%3C/path%3E%3Cdefs%3E%3ClinearGradient id='gradient2' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23c62828;stop-opacity:0.1'/%3E%3Cstop offset='100%25' style='stop-color:%23ff5f52;stop-opacity:0.1'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E")
          no-repeat;
        background-size: cover;
        z-index: 1;
      }
      .floor-gallery {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 2rem;
      }
      .floor-gallery img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
      }
      .floor-gallery img:hover {
        transform: scale(1.05);
      }
    </style>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {
                DEFAULT: "#c62828",
                dark: "#8e0000",
                light: "#ff5f52",
              },
            },
          },
        },
      };
    </script>
  </head>
  <body class="bg-gray-50">
    <!-- Navigation Bar -->
    <nav class="navbar fixed top-0 left-0 right-0 z-50 py-3">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between">
          <!-- Logo -->
          <div class="flex items-center">
            <img
              src="assets/images/haroon-logo.jpeg"
              alt="Haroon Shopping Center Logo"
              class="h-12 w-auto mr-3"
            />
            <span class="text-xl font-bold text-primary-dark"
              >Haroon Shopping Center</span
            >
          </div>

          <!-- Navigation Links -->
          <div class="hidden md:flex space-x-8">
            <a
              href="#home"
              class="text-gray-700 hover:text-primary transition duration-300"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-700 hover:text-primary transition duration-300"
              >About</a
            >
            <a
              href="#floors"
              class="text-gray-700 hover:text-primary transition duration-300"
              >Floors</a
            >
            <a
              href="#location"
              class="text-gray-700 hover:text-primary transition duration-300"
              >Location</a
            >
            <a
              href="#team"
              class="text-gray-700 hover:text-primary transition duration-300"
              >Team</a
            >
          </div>

          <!-- Mobile Menu Button -->
          <button
            class="md:hidden text-gray-700 hover:text-primary"
            id="mobileMenuBtn"
          >
            <i data-feather="menu"></i>
          </button>
        </div>

        <!-- Mobile Menu -->
        <div class="md:hidden mt-4 hidden" id="mobileMenu">
          <div class="flex flex-col space-y-2">
            <a
              href="#home"
              class="text-gray-700 hover:text-primary transition duration-300 py-2"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-700 hover:text-primary transition duration-300 py-2"
              >About</a
            >
            <a
              href="#floors"
              class="text-gray-700 hover:text-primary transition duration-300 py-2"
              >Floors</a
            >
            <a
              href="#location"
              class="text-gray-700 hover:text-primary transition duration-300 py-2"
              >Location</a
            >
            <a
              href="#team"
              class="text-gray-700 hover:text-primary transition duration-300 py-2"
              >Team</a
            >
          </div>
        </div>
      </div>
    </nav>

    <!-- Header with Banner -->
    <header
      class="relative w-full h-96 md:h-[500px] lg:h-[600px] overflow-hidden"
      id="home"
    >
      <img
        src="assets/images/banner.jpeg"
        alt="Haroon Shopping Center"
        class="w-full h-full object-cover object-left"
      />
      <div
        class="absolute inset-0 hero-overlay flex items-center justify-end pr-8 md:pr-16"
      >
        <div class="text-right">
          <h1
            class="text-3xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white text-glow mb-4"
            data-aos="fade-left"
            data-aos-duration="1000"
          >
            HAROON<br />SHOPPING<br />CENTER
          </h1>
          <p
            class="text-lg md:text-xl lg:text-2xl text-white opacity-90"
            data-aos="fade-left"
            data-aos-delay="300"
          >
            Your Ultimate Shopping Destination
          </p>
        </div>
      </div>
    </header>

    <!-- Timing Section -->
    <section class="py-8 bg-primary text-white" data-aos="fade-up">
      <div class="container mx-auto px-4">
        <div class="text-center">
          <h2 class="text-2xl md:text-3xl font-bold mb-2">Opening Hours</h2>
          <p class="text-xl">Monday - Thursday: 9:00 AM to 9:00 PM</p>
          <p class="text-xl">Friday: 9:00 AM to 3:00 PM</p>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section class="py-16 bg-white wave-bottom" id="about">
      <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Text Content -->
            <div data-aos="fade-right">
              <h2 class="text-3xl md:text-4xl font-bold text-primary-dark mb-6">
                Welcome to Haroon Shopping Center
              </h2>
              <p class="text-lg text-gray-700 mb-6">
                Haroon Shopping Center is a modern retail destination offering a
                wide variety of shops and services under one roof. With ten
                floors of carefully curated shopping experiences, we bring
                together local and international brands for a complete shopping
                experience.
              </p>
              <p class="text-lg text-gray-700">
                Experience the perfect blend of traditional hospitality and
                modern convenience as you explore our diverse collection of
                stores, each offering unique products and exceptional service.
              </p>
            </div>

            <!-- Video Content -->
            <div data-aos="fade-left">
              <div
                class="relative rounded-lg overflow-hidden shadow-lg max-h-80 lg:max-h-96"
              >
                <video
                  autoplay
                  muted
                  loop
                  playsinline
                  class="w-full h-full object-cover"
                >
                  <source
                    src="assets/video/shopping-mall-video.mp4"
                    type="video/mp4"
                  />
                  Your browser does not support the video tag.
                </video>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Floors Section Header -->
    <section
      class="py-16 bg-gradient-to-r from-primary to-primary-light text-white wave-top"
      id="floors"
    >
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center" data-aos="fade-up">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">
            Explore Our Floors
          </h2>
          <p class="text-lg mb-8">
            Ten floors of carefully curated shopping experiences, each offering
            unique products and services to meet all your needs under one roof.
          </p>
        </div>
      </div>
    </section>

    <!-- Floor 1 Section -->
    <section class="py-16 bg-white wave-bottom" id="floor1">
      <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-primary-dark text-center mb-8"
            data-aos="fade-up"
          >
            1st Floor - Men's Fashion
          </h2>
          <p
            class="text-lg text-gray-700 text-center mb-8"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            Discover an exclusive collection of men's clothing shops featuring
            the latest trends and styles. From formal wear to casual outfits,
            find everything you need to elevate your wardrobe.
          </p>
          <div class="floor-gallery" data-aos="fade-up" data-aos-delay="200">
            <img
              src="http://static.photos/fashion/640x360/1"
              alt="Men's Clothing Store 1"
            />
            <img
              src="http://static.photos/fashion/640x360/11"
              alt="Men's Clothing Store 2"
            />
            <img
              src="http://static.photos/fashion/640x360/21"
              alt="Men's Clothing Store 3"
            />
            <img
              src="http://static.photos/fashion/640x360/31"
              alt="Men's Clothing Store 4"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Floor 2 Section -->
    <section
      class="py-16 bg-gradient-to-br from-red-50 to-pink-50 wave-top"
      id="floor2"
    >
      <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-primary-dark text-center mb-8"
            data-aos="fade-up"
          >
            2nd Floor - Technology & Accessories
          </h2>
          <p
            class="text-lg text-gray-700 text-center mb-8"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            Mobile accessories, watches, clocks, and artificial jewelry from top
            brands. Stay connected and stylish with our comprehensive selection
            of tech accessories and timepieces.
          </p>
          <div class="floor-gallery" data-aos="fade-up" data-aos-delay="200">
            <img
              src="http://static.photos/technology/640x360/2"
              alt="Mobile Accessories"
            />
            <img
              src="http://static.photos/technology/640x360/12"
              alt="Watches and Clocks"
            />
            <img
              src="http://static.photos/technology/640x360/22"
              alt="Jewelry Store"
            />
            <img
              src="http://static.photos/technology/640x360/32"
              alt="Tech Accessories"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Floor 3 Section -->
    <section class="py-16 bg-white wave-bottom" id="floor3">
      <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-primary-dark text-center mb-8"
            data-aos="fade-up"
          >
            3rd Floor - Children's Paradise
          </h2>
          <p
            class="text-lg text-gray-700 text-center mb-8"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            Children's wear, boys' and girls' clothing, and accessories for all
            ages. Create magical moments with our delightful collection designed
            especially for your little ones.
          </p>
          <div class="floor-gallery" data-aos="fade-up" data-aos-delay="200">
            <img
              src="http://static.photos/children/640x360/3"
              alt="Children's Clothing"
            />
            <img
              src="http://static.photos/children/640x360/13"
              alt="Boys' Wear"
            />
            <img
              src="http://static.photos/children/640x360/23"
              alt="Girls' Wear"
            />
            <img
              src="http://static.photos/children/640x360/33"
              alt="Kids Accessories"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Floor 4 Section -->
    <section class="py-16 bg-gray-50 wave-top" id="floor4">
      <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-primary-dark text-center mb-8"
            data-aos="fade-up"
          >
            4th Floor - Beauty & Fragrance
          </h2>
          <p
            class="text-lg text-gray-700 text-center mb-8"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            Cosmetics and perfumes for both men and women, featuring
            international brands. Enhance your natural beauty with our premium
            collection of skincare, makeup, and fragrances.
          </p>
          <div class="floor-gallery" data-aos="fade-up" data-aos-delay="200">
            <img
              src="http://static.photos/beauty/640x360/4"
              alt="Cosmetics Store"
            />
            <img
              src="http://static.photos/beauty/640x360/14"
              alt="Perfume Collection"
            />
            <img
              src="http://static.photos/beauty/640x360/24"
              alt="Skincare Products"
            />
            <img
              src="http://static.photos/beauty/640x360/34"
              alt="Makeup Counter"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Floor 5 Section -->
    <section
      class="py-16 bg-gradient-to-br from-primary/10 to-primary-light/10 wave-bottom"
      id="floor5"
    >
      <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-primary-dark text-center mb-8"
            data-aos="fade-up"
          >
            5th Floor - Footwear Collection
          </h2>
          <p
            class="text-lg text-gray-700 text-center mb-8"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            Male and female shoes with a wide selection for all ages and
            occasions. Step into comfort and style with our extensive range of
            footwear from casual to formal.
          </p>
          <div class="floor-gallery" data-aos="fade-up" data-aos-delay="200">
            <img
              src="http://static.photos/footwear/640x360/5"
              alt="Men's Shoes"
            />
            <img
              src="http://static.photos/footwear/640x360/15"
              alt="Women's Shoes"
            />
            <img
              src="http://static.photos/footwear/640x360/25"
              alt="Sports Footwear"
            />
            <img
              src="http://static.photos/footwear/640x360/35"
              alt="Formal Shoes"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Floor 6 Section -->
    <section class="py-16 bg-gray-50 wave-top" id="floor6">
      <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-primary-dark text-center mb-8"
            data-aos="fade-up"
          >
            6th Floor - Home & Textiles
          </h2>
          <p
            class="text-lg text-gray-700 text-center mb-8"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            Garments, home textiles, and stylish home decoration items for your
            living space. Transform your home with our beautiful collection of
            fabrics, furnishings, and decorative pieces.
          </p>
          <div class="floor-gallery" data-aos="fade-up" data-aos-delay="200">
            <img
              src="http://static.photos/home/<USER>/6"
              alt="Home Textiles"
            />
            <img
              src="http://static.photos/home/<USER>/16"
              alt="Home Decoration"
            />
            <img
              src="http://static.photos/home/<USER>/26"
              alt="Fabric Store"
            />
            <img
              src="http://static.photos/home/<USER>/36"
              alt="Home Furnishings"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Floor 7 Section -->
    <section class="py-16 bg-white wave-bottom" id="floor7">
      <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-primary-dark text-center mb-8"
            data-aos="fade-up"
          >
            7th Floor - Women's Fashion & Beauty
          </h2>
          <p
            class="text-lg text-gray-700 text-center mb-8"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            Women's apparel and a dedicated cosmetic mall with premium beauty
            products. Discover the latest fashion trends and indulge in luxury
            beauty treatments.
          </p>
          <div class="floor-gallery" data-aos="fade-up" data-aos-delay="200">
            <img
              src="http://static.photos/fashion/640x360/7"
              alt="Women's Apparel"
            />
            <img
              src="http://static.photos/fashion/640x360/17"
              alt="Designer Wear"
            />
            <img
              src="http://static.photos/fashion/640x360/27"
              alt="Cosmetic Mall"
            />
            <img
              src="http://static.photos/fashion/640x360/37"
              alt="Beauty Salon"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Floor 8 Section -->
    <section
      class="py-16 bg-gradient-to-br from-red-100 to-primary/20 wave-top"
      id="floor8"
    >
      <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-primary-dark text-center mb-8"
            data-aos="fade-up"
          >
            8th Floor - Wedding Collection
          </h2>
          <p
            class="text-lg text-gray-700 text-center mb-8"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            Wedding attire and bridal accessories for your special day. Make
            your dream wedding come true with our exquisite collection of bridal
            wear and wedding essentials.
          </p>
          <div class="floor-gallery" data-aos="fade-up" data-aos-delay="200">
            <img
              src="http://static.photos/wedding/640x360/8"
              alt="Wedding Attire"
            />
            <img
              src="http://static.photos/wedding/640x360/18"
              alt="Bridal Wear"
            />
            <img
              src="http://static.photos/wedding/640x360/28"
              alt="Wedding Accessories"
            />
            <img
              src="http://static.photos/wedding/640x360/38"
              alt="Groom Collection"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Floor 9 Section -->
    <section class="py-16 bg-white wave-bottom" id="floor9">
      <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-primary-dark text-center mb-8"
            data-aos="fade-up"
          >
            9th Floor - Bags, Luggage & Furniture
          </h2>
          <p
            class="text-lg text-gray-700 text-center mb-8"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            Bags, handbags, luggage, and sofas. Spacious restrooms available.
            Find the perfect travel companions and comfortable furniture for
            your home.
          </p>
          <div class="floor-gallery" data-aos="fade-up" data-aos-delay="200">
            <img
              src="http://static.photos/accessories/640x360/9"
              alt="Bags and Luggage"
            />
            <img
              src="http://static.photos/accessories/640x360/19"
              alt="Handbags Collection"
            />
            <img
              src="http://static.photos/accessories/640x360/29"
              alt="Travel Luggage"
            />
            <img
              src="http://static.photos/accessories/640x360/39"
              alt="Furniture Store"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Floor 10 Section -->
    <section class="py-16 bg-gray-50 wave-top" id="floor10">
      <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-primary-dark text-center mb-8"
            data-aos="fade-up"
          >
            10th Floor - Administration
          </h2>
          <p
            class="text-lg text-gray-700 text-center mb-8"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            Staff and management offices. Information and administration
            services. Our dedicated team ensures smooth operations and excellent
            customer service throughout the shopping center.
          </p>
          <div class="floor-gallery" data-aos="fade-up" data-aos-delay="200">
            <img
              src="http://static.photos/office/640x360/10"
              alt="Management Offices"
            />
            <img
              src="http://static.photos/office/640x360/20"
              alt="Administration"
            />
            <img
              src="http://static.photos/office/640x360/30"
              alt="Customer Service"
            />
            <img
              src="http://static.photos/office/640x360/40"
              alt="Information Desk"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Experience Section -->
    <section class="py-16 bg-primary text-white wave-top">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center" data-aos="fade-up">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">
            A Complete Shopping Experience
          </h2>
          <p class="text-lg mb-8">
            Each section features high-quality products and attentive customer
            service, making every visit convenient and enjoyable for families
            and individuals. Haroon Shopping Center brings together local and
            international brands under one roof, creating the ultimate shopping
            destination.
          </p>
        </div>
      </div>
    </section>

    <!-- Location Section -->
    <section class="py-16 bg-white wave-bottom" id="location">
      <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row items-center">
          <div class="md:w-1/2 mb-8 md:mb-0 md:pr-8" data-aos="fade-right">
            <h2 class="text-3xl font-bold text-primary-dark mb-4">
              Our Location
            </h2>
            <p class="text-gray-700 mb-4">
              Visit us at our convenient location in the heart of the city.
              We're easily accessible with ample parking space available.
            </p>
            <address class="text-lg not-italic mb-4">
              <strong>Haroon Shopping Center</strong><br />
              123 Main Commercial Area<br />
              City Center, Quetta<br />
              Pakistan
            </address>
            <a
              href="https://maps.app.goo.gl/aMA3E5EB25ZsSYeg9"
              target="_blank"
              class="inline-flex items-center text-primary hover:text-primary-dark"
            >
              <i data-feather="map-pin" class="mr-2"></i>
              View on Google Maps
            </a>
          </div>
          <div class="md:w-1/2" data-aos="fade-left">
            <div
              class="max-h-80 lg:max-h-96 overflow-hidden rounded-lg shadow-lg"
            >
              <img
                src="assets/images/haroon-map.jpg"
                alt="Shopping Center Location"
                class="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Team Section -->
    <section class="py-16 bg-gray-50 wave-top" id="team">
      <div class="container mx-auto px-4">
        <h2
          class="text-3xl font-bold text-primary-dark text-center mb-12"
          data-aos="fade-up"
        >
          Our Leadership
        </h2>

        <div class="flex flex-col md:flex-row justify-center gap-8">
          <!-- Owner -->
          <div
            class="bg-white p-6 rounded-lg shadow-lg text-center max-w-xs mx-auto"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <img
              src="assets/images/dr-haroon.jpeg"
              alt="Dr. Haroon Khan"
              class="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
            />
            <h3 class="text-xl font-bold text-primary-dark mb-2">
              Dr. Haroon Khan
            </h3>
            <p class="text-gray-600 mb-2">Founder & Owner</p>
            <p class="text-gray-700">
              A visionary entrepreneur with decades of experience in retail, Dr.
              Haroon Khan established the shopping center to provide a premium
              shopping experience to the community.
            </p>
          </div>

          <!-- Manager -->
          <div
            class="bg-white p-6 rounded-lg shadow-lg text-center max-w-xs mx-auto"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <img
              src="assets/images/waheed-md.jpeg"
              alt="Waheed Khan"
              class="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
            />
            <h3 class="text-xl font-bold text-primary-dark mb-2">
              Waheed Khan
            </h3>
            <p class="text-gray-600 mb-2">Managing Director</p>
            <p class="text-gray-700">
              With exceptional leadership skills, Waheed Khan oversees daily
              operations, ensuring the shopping center maintains its high
              standards of service and quality.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary-dark text-white py-12">
      <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row justify-between">
          <div class="mb-8 md:mb-0">
            <h3 class="text-2xl font-bold mb-4">Haroon Shopping Center</h3>
            <p class="mb-4">Your ultimate shopping destination since 2005.</p>
            <div class="flex space-x-4">
              <a
                href="https://www.facebook.com/share/1FL5sYMhL9/"
                target="_blank"
                class="hover:text-primary-light"
              >
                <i data-feather="facebook"></i>
              </a>
              <a
                href="https://www.instagram.com/haroonshoppingcenter8"
                target="_blank"
                class="hover:text-primary-light"
              >
                <i data-feather="instagram"></i>
              </a>
              <a
                href="https://youtube.com/@haroonshoppingcenterquetta2635"
                target="_blank"
                class="hover:text-primary-light"
              >
                <i data-feather="youtube"></i>
              </a>
            </div>
          </div>
          <div class="grid grid-cols-2 gap-8">
            <div>
              <h4 class="text-lg font-bold mb-4">Quick Links</h4>
              <ul class="space-y-2">
                <li><a href="#" class="hover:text-primary-light">Home</a></li>
                <li>
                  <a href="#floor1" class="hover:text-primary-light">Floors</a>
                </li>
                <li>
                  <a href="#location" class="hover:text-primary-light"
                    >Location</a
                  >
                </li>
                <li>
                  <a href="#team" class="hover:text-primary-light">Our Team</a>
                </li>
              </ul>
            </div>
            <div>
              <h4 class="text-lg font-bold mb-4">Contact</h4>
              <ul class="space-y-2">
                <li class="flex items-center">
                  <i data-feather="phone" class="mr-2"></i>
                  <span>03218141718</span>
                </li>
                <li class="flex items-center">
                  <i data-feather="mail" class="mr-2"></i>
                  <span><EMAIL></span>
                </li>
                <li class="flex items-center">
                  <i data-feather="map-pin" class="mr-2"></i>
                  <span>Quetta, Pakistan</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="border-t border-primary mt-8 pt-8">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-center md:text-left mb-4 md:mb-0">
              &copy; 2023 Haroon Shopping Center. All rights reserved.
            </p>
            <div class="text-center md:text-right text-sm opacity-75">
              <p class="mb-1">
                Website by <span class="font-semibold">Kairos</span>
              </p>
              <p>
                Developed By: Kaleem Durrani |
                <a
                  href="mailto:<EMAIL>"
                  class="hover:text-primary-light"
                  ><EMAIL></a
                >
                |
                <a
                  href="https://wa.me/923498364816"
                  target="_blank"
                  class="hover:text-primary-light"
                  >03498364816</a
                >
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- Floating Contact Button -->
    <div class="fixed bottom-8 right-8 z-50">
      <button
        id="floatingBtn"
        class="floating-btn bg-primary text-white rounded-full p-4 shadow-lg hover:shadow-xl"
      >
        <i data-feather="message-square"></i>
      </button>
      <div
        id="floatingMenu"
        class="floating-menu absolute bottom-16 right-0 bg-white rounded-lg shadow-lg p-4 w-64"
      >
        <div class="space-y-3">
          <a
            href="tel:+923218141718"
            class="flex items-center text-gray-800 hover:text-primary"
          >
            <i data-feather="phone" class="mr-2"></i>
            <span>Call Us</span>
          </a>
          <a
            href="https://wa.me/923218141718"
            target="_blank"
            class="flex items-center text-gray-800 hover:text-primary"
          >
            <i data-feather="message-circle" class="mr-2"></i>
            <span>WhatsApp</span>
          </a>
          <a
            href="mailto:<EMAIL>"
            class="flex items-center text-gray-800 hover:text-primary"
          >
            <i data-feather="mail" class="mr-2"></i>
            <span>Email Us</span>
          </a>
          <a
            href="https://www.facebook.com/share/1FL5sYMhL9/"
            target="_blank"
            class="flex items-center text-gray-800 hover:text-primary"
          >
            <i data-feather="facebook" class="mr-2"></i>
            <span>Facebook</span>
          </a>
          <a
            href="https://maps.app.goo.gl/aMA3E5EB25ZsSYeg9"
            target="_blank"
            class="flex items-center text-gray-800 hover:text-primary"
          >
            <i data-feather="map" class="mr-2"></i>
            <span>View Map</span>
          </a>
        </div>
      </div>
    </div>

    <script>
      // Initialize AOS
      AOS.init({
        duration: 800,
        easing: "ease-in-out",
        once: true,
      });

      // Initialize Feather Icons
      feather.replace();

      // Navbar scroll effect
      window.addEventListener("scroll", function () {
        const navbar = document.querySelector(".navbar");
        if (window.scrollY > 50) {
          navbar.classList.add("scrolled");
        } else {
          navbar.classList.remove("scrolled");
        }
      });

      // Mobile menu toggle
      const mobileMenuBtn = document.getElementById("mobileMenuBtn");
      const mobileMenu = document.getElementById("mobileMenu");

      mobileMenuBtn.addEventListener("click", function () {
        mobileMenu.classList.toggle("hidden");
      });

      // Close mobile menu when clicking on links
      const mobileLinks = mobileMenu.querySelectorAll("a");
      mobileLinks.forEach((link) => {
        link.addEventListener("click", function () {
          mobileMenu.classList.add("hidden");
        });
      });

      // Floating Button Interaction
      const floatingBtn = document.getElementById("floatingBtn");
      const floatingMenu = document.getElementById("floatingMenu");

      floatingBtn.addEventListener("click", () => {
        floatingMenu.classList.toggle("active");
      });

      // Close menu when clicking outside
      document.addEventListener("click", (e) => {
        if (
          !floatingBtn.contains(e.target) &&
          !floatingMenu.contains(e.target)
        ) {
          floatingMenu.classList.remove("active");
        }
      });

      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          document.querySelector(this.getAttribute("href")).scrollIntoView({
            behavior: "smooth",
          });
        });
      });

      // Text glow animation
      anime({
        targets: ".text-glow",
        opacity: [0.8, 1],
        duration: 2000,
        loop: true,
        direction: "alternate",
        easing: "easeInOutSine",
      });
    </script>
  </body>
</html>
